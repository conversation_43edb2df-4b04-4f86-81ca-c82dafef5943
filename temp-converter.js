
      const { app, nativeImage } = require('electron');
      const fs = require('fs');
      const path = require('path');

      app.whenReady().then(() => {
        const svgPath = path.join(__dirname, 'assets', 'icon.svg');
        const icoPath = path.join(__dirname, 'build', 'icon.ico');

        try {
          // Read SVG file
          const svgBuffer = fs.readFileSync(svgPath);

          // Convert to native image and resize
          const img = nativeImage.createFromBuffer(svgBuffer);
          const resizedImg = img.resize({ width: 256, height: 256 });

          // Save as PNG (electron-builder will handle ICO conversion)
          fs.writeFileSync(icoPath.replace('.ico', '.png'), resizedImg.toPNG());

          console.log('Icon converted successfully');
        } catch (error) {
          console.error('Error converting icon:', error);
        }

        app.quit();
      });
    