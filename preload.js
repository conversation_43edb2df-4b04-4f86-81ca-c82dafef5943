// Preload script for Electron
const { ipc<PERSON><PERSON><PERSON>, contextBridge } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('tasbeehAPI', {
  // Get tasbeeh data from main process
  getTasbeehData: () => {
    ipcRenderer.send('get-tasbeeh-data');
  },
  
  // Update tasbeeh count
  updateCount: (type) => {
    ipcRenderer.send('update-tasbeeh-count', type);
  },
  
  // Reset tasbeeh count
  resetCount: (type) => {
    ipcRenderer.send('reset-tasbeeh-count', type);
  },
  
  // Update reminder settings
  updateReminderSettings: (settings) => {
    ipcRenderer.send('update-reminder-settings', settings);
  },
  
  // Listen for tasbeeh data updates
  onTasbeehDataReceived: (callback) => {
    ipcRenderer.on('tasbeeh-data', (_, data) => callback(data));
  },
  
  // Listen for tasbeeh data updates
  onTasbeehDataUpdated: (callback) => {
    ipcRenderer.on('tasbeeh-data-updated', (_, data) => callback(data));
  }
});