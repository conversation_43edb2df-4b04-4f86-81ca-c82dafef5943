# Changelog

All notable changes to the Tasbeeh App will be documented in this file.

## [1.1.0] - 2025-07-04

### Changed
- **Full Height Layout**: Modified app window to fill the entire right side height of the screen
  - Window height now dynamically adjusts to screen work area height
  - Window positioned at top-right corner (x: screen_width - 300, y: 0)
  - Improved visual presence and accessibility

### Technical Details
- Updated `createWindow()` function in `main.js` to use `screen.getPrimaryDisplay().workAreaSize.height`
- Changed window y-position from fixed 100px to 0 for full height coverage
- Maintained 300px width for optimal usability while maximizing vertical space

### Files Modified
- `main.js`: Updated window creation parameters for full-height display
- `styles.css`: Changed app container height from fixed 500px to 100vh for full viewport height
- `build.js`: Fixed build directory creation and icon conversion process
- `package.json`: Enhanced electron-builder configuration with NSIS installer settings
- `README.md`: Added comprehensive build and sharing instructions

### Files Added
- `build-installer.bat`: One-click installer builder for Windows

### Added
- **Installer Creation**: Added proper Windows installer (.exe) generation
  - Created `build-installer.bat` for easy one-click building
  - Enhanced electron-builder configuration with NSIS installer options
  - Added desktop and start menu shortcuts creation
  - Improved icon handling and build directory management

### Benefits
- Better screen real estate utilization
- More visible and accessible app interface
- Consistent positioning across different screen resolutions
- Enhanced user experience with larger interaction area
- Easy sharing with friends through Windows installer
