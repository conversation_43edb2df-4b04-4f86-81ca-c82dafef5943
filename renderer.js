// Renderer process for Tasbeeh App

// DOM Elements
const totalCountDisplay = document.getElementById('total-count');
const tasbeehCards = document.querySelectorAll('.tasbeeh-card');
const settingsBtn = document.getElementById('settings-btn');
const minimizeBtn = document.getElementById('minimize-btn');
const settingsPanel = document.getElementById('settings-panel');
const reminderToggle = document.getElementById('reminder-toggle');
const reminderInterval = document.getElementById('reminder-interval');
const resetAllBtn = document.getElementById('reset-all-btn');
const saveSettingsBtn = document.getElementById('save-settings-btn');

// Current tasbeeh data
let tasbeehData = null;

// Initialize the app
function initApp() {
  console.log('Initializing app...');
  // Check if tasbeehAPI is available
  if (window.tasbeehAPI) {
    console.log('tasbeehAPI is available');
    // Request tasbeeh data from main process
    window.tasbeehAPI.getTasbeehData();
  } else {
    console.error('tasbeehAPI is not available');
  }
  
  // Set up event listeners
  setupEventListeners();
}

// Set up event listeners
function setupEventListeners() {
  console.log('Setting up event listeners...');
  // Tasbeeh card buttons
  tasbeehCards.forEach(card => {
    const type = card.dataset.type;
    const countBtn = card.querySelector('.count-btn');
    const resetBtn = card.querySelector('.reset-btn');
    
    console.log(`Setting up listeners for ${type}`);
    
    countBtn.addEventListener('click', () => {
      console.log(`Count button clicked for ${type}`);
      if (window.tasbeehAPI) {
        window.tasbeehAPI.updateCount(type);
        animateCounter(card.querySelector('.counter'));
      } else {
        console.error('tasbeehAPI is not available for updateCount');
      }
    });
    
    resetBtn.addEventListener('click', () => {
      console.log(`Reset button clicked for ${type}`);
      if (window.tasbeehAPI) {
        window.tasbeehAPI.resetCount(type);
      } else {
        console.error('tasbeehAPI is not available for resetCount');
      }
    });
  });
}
  
  // Settings button
  settingsBtn.addEventListener('click', () => {
    settingsPanel.classList.toggle('hidden');
  });
  
  // Minimize button
  minimizeBtn.addEventListener('click', () => {
    window.close();
  });
  
  // Reset all button
  resetAllBtn.addEventListener('click', () => {
    if (confirm('Are you sure you want to reset all counters?')) {
      window.tasbeehAPI.resetCount('all');
    }
  });
  
  // Save settings button
  saveSettingsBtn.addEventListener('click', () => {
    const settings = {
      enabled: reminderToggle.checked,
      interval: parseInt(reminderInterval.value) * 60000 // Convert minutes to milliseconds
    };
    
    window.tasbeehAPI.updateReminderSettings(settings);
    settingsPanel.classList.add('hidden');
  });
  
  // Listen for tasbeeh data from main process
  window.tasbeehAPI.onTasbeehDataReceived(updateUI);
  window.tasbeehAPI.onTasbeehDataUpdated(updateUI);
  
  // Keyboard shortcuts
  document.addEventListener('keydown', (event) => {
    // Escape key to close settings panel
    if (event.key === 'Escape') {
      settingsPanel.classList.add('hidden');
    }
    
    // Number keys 1-4 to increment counters
    if (event.key >= '1' && event.key <= '4') {
      const index = parseInt(event.key) - 1;
      if (index >= 0 && index < tasbeehCards.length) {
        const card = tasbeehCards[index];
        const type = card.dataset.type;
        window.tasbeehAPI.updateCount(type);
        animateCounter(card.querySelector('.counter'));
      }
    }
  });
}

// Update UI with tasbeeh data
function updateUI(data) {
  tasbeehData = data;
  
  // Update total count
  totalCountDisplay.textContent = data.totalCount;
  
  // Update individual counters
  tasbeehCards.forEach(card => {
    const type = card.dataset.type;
    const countElement = card.querySelector('.count');
    countElement.textContent = data.counts[type];
  });
  
  // Update settings
  reminderToggle.checked = data.reminderEnabled;
  reminderInterval.value = data.reminderInterval / 60000; // Convert milliseconds to minutes
}

// Animate counter when incremented
function animateCounter(counterElement) {
  counterElement.classList.add('animate');
  setTimeout(() => {
    counterElement.classList.remove('animate');
  }, 500);
}

// Initialize the app when the window is loaded
window.addEventListener('DOMContentLoaded', initApp);