const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('Tasbeeh App Build Script');
console.log('========================');

// Function to prepare build assets
function prepareAssets() {
  console.log('Preparing build assets...');

  try {
    // Ensure build directory exists
    const buildDir = path.join(__dirname, 'build');
    if (!fs.existsSync(buildDir)) {
      fs.mkdirSync(buildDir, { recursive: true });
    }

    // Copy SVG icon for electron-builder (it can handle SVG files)
    const svgPath = path.join(__dirname, 'assets', 'icon.svg');
    const buildIconPath = path.join(__dirname, 'build', 'icon.svg');

    if (fs.existsSync(svgPath)) {
      fs.copyFileSync(svgPath, buildIconPath);
      console.log('Icon prepared successfully');
    } else {
      console.log('Warning: Icon file not found, using default Electron icon');
    }

  } catch (error) {
    console.error('Error preparing assets:', error);
    console.log('Continuing with build process...');
  }
}

// Main build function
async function build() {
  console.log('Starting build process...');

  // Prepare assets
  prepareAssets();

  // Run electron-builder with options to avoid permission issues
  console.log('Building application with electron-builder...');
  try {
    // Use environment variable to skip problematic dependencies
    const env = {
      ...process.env,
      ELECTRON_BUILDER_ALLOW_UNRESOLVED_DEPENDENCIES: 'true',
      ELECTRON_SKIP_BINARY_DOWNLOAD: 'false'
    };

    execSync('npx electron-builder --win --x64', {
      stdio: 'inherit',
      env: env
    });
    console.log('Build completed successfully!');
  } catch (error) {
    console.error('Build failed:', error);
    process.exit(1);
  }
}

// Run the build
build();