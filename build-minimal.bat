@echo off
echo ========================================
echo    Building Tasbeeh App Installer
echo    (Minimal Version - Default Icon)
echo ========================================
echo.

REM Check if Node.js is installed
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ERROR: Node.js is not installed. Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if dependencies are installed
if not exist node_modules (
    echo Dependencies not found. Installing...
    call npm install
    
    if %ERRORLEVEL% neq 0 (
        echo ERROR: Failed to install dependencies.
        pause
        exit /b 1
    )
    echo.
)

REM Build with minimal configuration
echo Building the installer...
echo This may take a few minutes...
echo Note: Using default Electron icon for now
echo.

REM Set environment variables to avoid issues
set ELECTRON_BUILDER_ALLOW_UNRESOLVED_DEPENDENCIES=true

REM Build with minimal options
npx electron-builder --win --x64

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: Build failed. Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Build Completed Successfully!
echo ========================================
echo.
echo Your installer has been created in the 'dist' folder.
echo Look for a file like 'Tasbeeh App Setup 1.0.0.exe'
echo.
echo The installer will use the default Electron icon.
echo You can customize the icon later once the basic build works.
echo.
echo Share the .exe file with your friends to install the app!
echo.
pause
