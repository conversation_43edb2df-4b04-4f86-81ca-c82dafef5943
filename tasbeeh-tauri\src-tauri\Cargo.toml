[package]
name = "tasbeeh-tauri"
version = "1.0.0"
description = "A lightweight Tasbeeh app"
authors = ["you"]
license = ""
repository = ""
default-run = "tasbeeh-tauri"
edition = "2021"
rust-version = "1.60"

[build-dependencies]
tauri-build = { version = "1.5.0", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
tauri = { version = "1.5.0", features = ["api-all", "system-tray", "notification-all"] }
tokio = { version = "1", features = ["full"] }

[features]
default = [ "custom-protocol" ]
custom-protocol = [ "tauri/custom-protocol" ]
