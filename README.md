# Tasbeeh App

A desktop application for Tasbeeh (Islamic remembrance) with reminder functionality. The app sits on the right side of the screen and expands on hover, allowing you to count different types of dhikr (remembrance phrases).

## Features

- Four common dhikr phrases with counters:
  - <PERSON> (لا إلَهَ إلاَّ اللهُ)
  - <PERSON><PERSON> (اللهُ أكْبَرُ)
  - <PERSON><PERSON><PERSON><PERSON><PERSON> (الْحَمْدُ للهِ)
  - <PERSON><PERSON><PERSON><PERSON> (سُبْحَانَ اللهِ)
- Reminder notifications at customizable intervals
- Minimalist interface that stays out of your way
- Automatic saving of counts
- Keyboard shortcuts (1-4 keys for quick counting)

## Installation

### Prerequisites

- [Node.js](https://nodejs.org/) (v14 or later)
- [npm](https://www.npmjs.com/) (usually comes with Node.js)

### Development Setup

1. Clone this repository or download the source code
2. Navigate to the project directory
3. Install dependencies:
   ```
   npm install
   ```
4. Start the application:
   ```
   npm start
   ```

### Building the Application

#### Quick Build (Recommended)
Double-click `build-installer.bat` to automatically build the installer.

#### Manual Build
To create an executable for your platform:

```
npm run build
```

This will generate the application in the `dist` directory.

#### Sharing with Friends
1. Run the build process (using either method above)
2. Navigate to the `dist` folder
3. Find the `.exe` installer file (e.g., `Tasbeeh App Setup 1.1.0.exe`)
4. Share this `.exe` file with your friends
5. They can double-click it to install the app on their computers

The installer will:
- Install the app to their system
- Create desktop and start menu shortcuts
- Allow them to choose the installation directory

## Usage

- The app will start minimized in the system tray
- Click the tray icon to show the app
- Click the counter buttons or use number keys (1-4) to increment counts
- Hover over the app to expand it when it's minimized
- Access settings by clicking the gear icon

## Keyboard Shortcuts

- `1` - Increment "La ilaha illa Allah" counter
- `2` - Increment "Allahu Akbar" counter
- `3` - Increment "Alhamdulillah" counter
- `4` - Increment "SubhanAllah" counter
- `Esc` - Close settings panel

## License

MIT