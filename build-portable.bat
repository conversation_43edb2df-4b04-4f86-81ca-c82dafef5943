@echo off
echo ========================================
echo    Building Tasbeeh App (Portable)
echo    No Installation Required!
echo ========================================
echo.

REM Check if Node.js is installed
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ERROR: Node.js is not installed. Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if dependencies are installed
if not exist node_modules (
    echo Dependencies not found. Installing...
    call npm install
    
    if %ERRORLEVEL% neq 0 (
        echo ERROR: Failed to install dependencies.
        pause
        exit /b 1
    )
    echo.
)

REM Build portable version
echo Building portable executable...
echo This creates a single .exe file that doesn't need installation!
echo This may take a few minutes...
echo.

REM Set environment variables to avoid issues
set ELECTRON_BUILDER_ALLOW_UNRESOLVED_DEPENDENCIES=true

REM Build portable version
npx electron-builder --win --x64

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: Build failed. Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Build Completed Successfully!
echo ========================================
echo.
echo Your portable app has been created in the 'dist' folder.
echo Look for a file like 'Tasbeeh App 1.0.0.exe'
echo.
echo This is a PORTABLE version:
echo - No installation required
echo - Just double-click the .exe to run
echo - Share this single file with your friends
echo - They can run it directly without installing anything
echo.
echo The app will use the default Electron icon.
echo.
pause
