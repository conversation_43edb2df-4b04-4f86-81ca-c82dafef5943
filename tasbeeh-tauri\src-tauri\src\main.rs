#![cfg_attr(
    all(not(debug_assertions), target_os = "windows"),
    windows_subsystem = "windows"
)]

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;
use std::sync::Mutex;
use tauri::{
    AppHandle, CustomMenuItem, Manager, State, SystemTray, SystemTrayEvent, SystemTrayMenu,
    SystemTrayMenuItem, Window,
};
use tokio::time::{interval, Duration};

#[derive(Debug, Serialize, Deserialize, Clone)]
struct TasbeehData {
    counts: HashMap<String, u32>,
    total_count: u32,
    reminder_enabled: bool,
    reminder_interval: u64, // in milliseconds
}

impl Default for TasbeehData {
    fn default() -> Self {
        let mut counts = HashMap::new();
        counts.insert("La ilaha illa Allah".to_string(), 0);
        counts.insert("Allah<PERSON> Akbar".to_string(), 0);
        counts.insert("Alhamdulillah".to_string(), 0);
        counts.insert("SubhanAllah".to_string(), 0);

        TasbeehData {
            counts,
            total_count: 0,
            reminder_enabled: true,
            reminder_interval: 60000, // 1 minute
        }
    }
}

type TasbeehState = Mutex<TasbeehData>;

fn get_data_file_path(app_handle: &AppHandle) -> PathBuf {
    app_handle
        .path_resolver()
        .app_data_dir()
        .unwrap()
        .join("tasbeeh-data.json")
}

fn load_tasbeeh_data(app_handle: &AppHandle) -> TasbeehData {
    let data_path = get_data_file_path(app_handle);
    
    if data_path.exists() {
        if let Ok(data_str) = fs::read_to_string(&data_path) {
            if let Ok(data) = serde_json::from_str::<TasbeehData>(&data_str) {
                return data;
            }
        }
    }
    
    TasbeehData::default()
}

fn save_tasbeeh_data(app_handle: &AppHandle, data: &TasbeehData) {
    let data_path = get_data_file_path(app_handle);
    
    // Create directory if it doesn't exist
    if let Some(parent) = data_path.parent() {
        let _ = fs::create_dir_all(parent);
    }
    
    if let Ok(data_str) = serde_json::to_string_pretty(data) {
        let _ = fs::write(&data_path, data_str);
    }
}

#[tauri::command]
fn get_tasbeeh_data(
    app_handle: AppHandle,
    state: State<TasbeehState>,
) -> Result<TasbeehData, String> {
    let mut data = state.lock().unwrap();
    *data = load_tasbeeh_data(&app_handle);
    Ok(data.clone())
}

#[tauri::command]
fn update_count(
    app_handle: AppHandle,
    state: State<TasbeehState>,
    dhikr_type: String,
) -> Result<TasbeehData, String> {
    let mut data = state.lock().unwrap();
    
    if let Some(count) = data.counts.get_mut(&dhikr_type) {
        *count += 1;
        data.total_count += 1;
        save_tasbeeh_data(&app_handle, &data);
    }
    
    Ok(data.clone())
}

#[tauri::command]
fn reset_count(
    app_handle: AppHandle,
    state: State<TasbeehState>,
    dhikr_type: String,
) -> Result<TasbeehData, String> {
    let mut data = state.lock().unwrap();
    
    if dhikr_type == "all" {
        for (_, count) in data.counts.iter_mut() {
            data.total_count -= *count;
            *count = 0;
        }
    } else if let Some(count) = data.counts.get_mut(&dhikr_type) {
        data.total_count -= *count;
        *count = 0;
    }
    
    save_tasbeeh_data(&app_handle, &data);
    Ok(data.clone())
}

#[tauri::command]
fn update_reminder_settings(
    app_handle: AppHandle,
    state: State<TasbeehState>,
    enabled: bool,
    interval: u64,
) -> Result<TasbeehData, String> {
    let mut data = state.lock().unwrap();
    data.reminder_enabled = enabled;
    data.reminder_interval = interval;
    save_tasbeeh_data(&app_handle, &data);
    Ok(data.clone())
}

async fn reminder_loop(app_handle: AppHandle, state: State<'_, TasbeehState>) {
    let mut interval_timer = interval(Duration::from_secs(60)); // Check every minute
    
    loop {
        interval_timer.tick().await;
        
        let (should_notify, reminder_interval) = {
            let data = state.lock().unwrap();
            (data.reminder_enabled, data.reminder_interval)
        };
        
        if should_notify {
            let _ = app_handle.notification()
                .builder()
                .title("Tasbeeh Reminder")
                .body("Time to remember Allah. Take a moment for dhikr.")
                .show();
        }
    }
}

fn main() {
    let quit = CustomMenuItem::new("quit".to_string(), "Quit");
    let show = CustomMenuItem::new("show".to_string(), "Show App");
    let toggle_reminder = CustomMenuItem::new("toggle_reminder".to_string(), "Toggle Reminder");
    
    let tray_menu = SystemTrayMenu::new()
        .add_item(show)
        .add_native_item(SystemTrayMenuItem::Separator)
        .add_item(toggle_reminder)
        .add_native_item(SystemTrayMenuItem::Separator)
        .add_item(quit);

    let system_tray = SystemTray::new().with_menu(tray_menu);

    tauri::Builder::default()
        .manage(TasbeehState::default())
        .system_tray(system_tray)
        .on_system_tray_event(|app, event| match event {
            SystemTrayEvent::LeftClick {
                position: _,
                size: _,
                ..
            } => {
                let window = app.get_window("main").unwrap();
                window.show().unwrap();
                window.set_focus().unwrap();
            }
            SystemTrayEvent::MenuItemClick { id, .. } => match id.as_str() {
                "quit" => {
                    std::process::exit(0);
                }
                "show" => {
                    let window = app.get_window("main").unwrap();
                    window.show().unwrap();
                    window.set_focus().unwrap();
                }
                "toggle_reminder" => {
                    let state = app.state::<TasbeehState>();
                    let mut data = state.lock().unwrap();
                    data.reminder_enabled = !data.reminder_enabled;
                    save_tasbeeh_data(app.app_handle(), &data);
                }
                _ => {}
            },
            _ => {}
        })
        .on_window_event(|event| match event.event() {
            tauri::WindowEvent::CloseRequested { api, .. } => {
                event.window().hide().unwrap();
                api.prevent_close();
            }
            _ => {}
        })
        .invoke_handler(tauri::generate_handler![
            get_tasbeeh_data,
            update_count,
            reset_count,
            update_reminder_settings
        ])
        .setup(|app| {
            let app_handle = app.handle();
            let state = app.state::<TasbeehState>();
            
            // Start reminder loop
            tauri::async_runtime::spawn(reminder_loop(app_handle.clone(), state.clone()));
            
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
