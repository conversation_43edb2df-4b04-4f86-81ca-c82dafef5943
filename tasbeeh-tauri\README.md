# Tasbeeh App - Tauri Version

A lightweight desktop application for Islamic Tasbeeh (dhikr counting) built with Tauri framework.

## Features

- **Lightweight**: ~15-30MB final app size (vs 700MB+ Electron version)
- **System Tray**: Runs in system tray with quick access
- **Reminder Notifications**: Configurable reminder intervals
- **Data Persistence**: Automatically saves your counts
- **Keyboard Shortcuts**: Use keys 1-4 to increment counters
- **Modern UI**: Beautiful gradient design with animations

## Size Comparison

- **Original Electron App**: ~726MB
- **Tauri Version**: ~15-30MB
- **Size Reduction**: ~95% smaller!

## Prerequisites

Before building, ensure you have:

1. **Rust** installed: https://rustup.rs/
2. **Node.js** installed (for npm commands)
3. **Visual Studio Build Tools** (Windows) or equivalent for your OS

## Installation & Setup

1. **Install Rust** (if not already installed):
   ```bash
   winget install Rustlang.Rust.MSVC
   ```

2. **Install Tauri CLI**:
   ```bash
   cargo install tauri-cli
   ```

3. **Navigate to the project directory**:
   ```bash
   cd tasbeeh-tauri
   ```

## Development

### Run in Development Mode
```bash
cargo tauri dev
```

### Build for Production
```bash
cargo tauri build
```

The built executable will be in `src-tauri/target/release/`

## Usage

- **Click** the + button to increment counters
- **Keyboard**: Use keys 1-4 to increment respective counters
- **Settings**: Click gear icon to configure reminders
- **System Tray**: App runs in system tray when minimized
- **Reset**: Individual reset buttons or reset all in settings

## Dhikr Included

1. **لا إله إلا الله** (La ilaha illa Allah)
2. **الله أكبر** (Allahu Akbar)  
3. **الحمد لله** (Alhamdulillah)
4. **سبحان الله** (SubhanAllah)

## Technical Details

- **Frontend**: HTML, CSS, JavaScript
- **Backend**: Rust with Tauri framework
- **UI**: Native system webview (no Chromium bundled)
- **Data Storage**: JSON file in user data directory
- **Notifications**: Native system notifications

## Benefits of Tauri vs Electron

- ✅ **95% smaller** file size
- ✅ **Lower memory usage**
- ✅ **Faster startup time**
- ✅ **Better security** (Rust backend)
- ✅ **Native look and feel**
