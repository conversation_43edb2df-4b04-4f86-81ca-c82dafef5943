@echo off
echo ========================================
echo    Building Tasbeeh App Installer
echo ========================================
echo.

REM Check if Node.js is installed
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ERROR: Node.js is not installed. Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if dependencies are installed
if not exist node_modules (
    echo Dependencies not found. Installing...
    call npm install
    
    if %ERRORLEVEL% neq 0 (
        echo ERROR: Failed to install dependencies.
        pause
        exit /b 1
    )
    echo.
)

REM Run the build process
echo Building the installer...
echo This may take a few minutes...
echo.

call npm run build

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: Build failed. Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Build Completed Successfully!
echo ========================================
echo.
echo Your installer has been created in the 'dist' folder.
echo You can find the .exe installer file there.
echo.
echo Share the .exe file with your friends to install the app!
echo.
pause
