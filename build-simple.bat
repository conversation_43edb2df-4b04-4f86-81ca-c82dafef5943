@echo off
echo ========================================
echo    Building Tasbeeh App Installer
echo    (Simple Version - No Code Signing)
echo ========================================
echo.

REM Check if Node.js is installed
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo ERROR: Node.js is not installed. Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if dependencies are installed
if not exist node_modules (
    echo Dependencies not found. Installing...
    call npm install
    
    if %ERRORLEVEL% neq 0 (
        echo ERROR: Failed to install dependencies.
        pause
        exit /b 1
    )
    echo.
)

REM Create build directory and copy icon
echo Preparing build assets...
if not exist build mkdir build
copy assets\icon.svg build\icon.svg >nul 2>nul

REM Run electron-builder directly with simplified options
echo Building the installer...
echo This may take a few minutes...
echo.

REM Set environment variables to avoid permission issues
set ELECTRON_BUILDER_ALLOW_UNRESOLVED_DEPENDENCIES=true
set ELECTRON_SKIP_BINARY_DOWNLOAD=false

REM Build with specific options to avoid code signing issues
npx electron-builder --win --x64 --config.win.sign=null

if %ERRORLEVEL% neq 0 (
    echo.
    echo ERROR: Build failed. Trying alternative method...
    echo.
    
    REM Try with even simpler options
    npx electron-builder --win --x64 --config.nsis.oneClick=false --config.win.sign=null --config.directories.output=dist
    
    if %ERRORLEVEL% neq 0 (
        echo ERROR: Build failed with both methods. Please check the error messages above.
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo    Build Completed Successfully!
echo ========================================
echo.
echo Your installer has been created in the 'dist' folder.
echo Look for a file like 'Tasbeeh App Setup 1.0.0.exe'
echo.
echo Share the .exe file with your friends to install the app!
echo.
pause
