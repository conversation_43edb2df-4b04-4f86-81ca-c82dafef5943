{"build": {"beforeDevCommand": "", "beforeBuildCommand": "", "devPath": "../dist", "distDir": "../dist", "withGlobalTauri": false}, "package": {"productName": "Tasbeeh App", "version": "1.0.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "notification": {"all": true}, "fs": {"all": false, "readFile": true, "writeFile": true, "createDir": true, "exists": true}, "path": {"all": true}}, "bundle": {"active": true, "targets": ["msi", "exe"], "identifier": "com.tasbeeh.app", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": [], "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}}, "security": {"csp": null}, "windows": [{"fullscreen": false, "resizable": false, "title": "Tasbeeh App", "width": 300, "height": 600, "alwaysOnTop": false, "skipTaskbar": true, "decorations": false, "transparent": true}], "systemTray": {"iconPath": "icons/icon.ico", "iconAsTemplate": true, "menuOnLeftClick": false}}}