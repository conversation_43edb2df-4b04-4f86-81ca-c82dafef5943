<svg xmlns="http://www.w3.org/2000/svg" width="256" height="256" viewBox="0 0 256 256">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecca3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2e8b57;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="5" />
      <feOffset dx="0" dy="2" result="offsetblur" />
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.3" />
      </feComponentTransfer>
      <feMerge>
        <feMergeNode />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="128" cy="128" r="120" fill="url(#grad1)" filter="url(#shadow)" />
  
  <!-- Tasbeeh Beads -->
  <circle cx="128" cy="60" r="15" fill="white" fill-opacity="0.9" />
  <circle cx="128" cy="95" r="15" fill="white" fill-opacity="0.9" />
  <circle cx="128" cy="130" r="15" fill="white" fill-opacity="0.9" />
  <circle cx="128" cy="165" r="15" fill="white" fill-opacity="0.9" />
  <circle cx="128" cy="200" r="15" fill="white" fill-opacity="0.9" />
  
  <!-- Connecting Thread -->
  <line x1="128" y1="45" x2="128" y2="215" stroke="white" stroke-opacity="0.7" stroke-width="3" stroke-dasharray="0, 17" stroke-linecap="round" />
  
  <!-- Arabic Text -->
  <text x="128" y="128" font-family="Arial, sans-serif" font-size="40" font-weight="bold" text-anchor="middle" fill="white" filter="url(#shadow)">تسبيح</text>
</svg>